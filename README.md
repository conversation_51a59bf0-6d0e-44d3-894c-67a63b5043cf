# Modern Landing Page

A beautiful, responsive single-page landing page built with **Vite** and **Tailwind CSS 4**.

## Features

- ⚡ **Vite** for lightning-fast development
- 🎨 **Tailwind CSS 4** with the latest features
- 📱 **Fully responsive** design
- ✨ **Smooth animations** and interactions
- 🚀 **Modern build tools** and optimization

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

### Development

Start the development server:
```bash
npm run dev
```

Open [http://localhost:5173](http://localhost:5173) in your browser.

### Build for Production

Create an optimized production build:
```bash
npm run build
```

Preview the production build:
```bash
npm run preview
```

## Project Structure

```
landing-page/
├── public/          # Static assets
├── src/
│   ├── main.js      # Main JavaScript file
│   └── style.css    # Tailwind CSS imports and custom styles
├── index.html       # HTML template
├── vite.config.js   # Vite configuration with Tailwind CSS 4
└── package.json     # Dependencies and scripts
```

## Tailwind CSS 4 Features Used

- **Zero Configuration**: No `tailwind.config.js` needed
- **CSS-first Configuration**: Using `@import "tailwindcss"`
- **Vite Plugin**: Optimized integration with `@tailwindcss/vite`
- **Modern CSS**: Leveraging the latest CSS features
- **Improved Performance**: Faster builds and smaller bundles

## Customization

### Colors and Styling
Modify the Tailwind classes in `src/main.js` to change colors, spacing, and layout.

### Content
Update the text content, images, and links in `src/main.js` to match your brand.

### Additional Styles
Add custom CSS in `src/style.css` for any additional styling needs.

## Technologies Used

- [Vite](https://vite.dev/) - Next generation frontend tooling
- [Tailwind CSS 4](https://tailwindcss.com/) - Utility-first CSS framework
- Vanilla JavaScript - For interactive features
- Modern CSS - Animations and responsive design

## Browser Support

This project uses Tailwind CSS 4, which targets modern browsers:
- Chrome 111+
- Safari 16.4+
- Firefox 128+

## License

MIT License - feel free to use this project for your own landing pages!